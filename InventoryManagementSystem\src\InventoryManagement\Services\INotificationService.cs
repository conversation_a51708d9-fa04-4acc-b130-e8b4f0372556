using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using InventoryManagement.Models;

namespace InventoryManagement.Services
{
    /// <summary>
    /// Interface for notification services
    /// </summary>
    public interface INotificationService
    {
        /// <summary>
        /// Sends a notification to a specific user
        /// </summary>
        /// <param name="userId">ID of the user to notify</param>
        /// <param name="title">Notification title</param>
        /// <param name="message">Notification message</param>
        /// <param name="type">Type of notification</param>
        /// <param name="priority">Priority level</param>
        /// <returns>Task representing the async operation</returns>
        Task SendNotificationAsync(int userId, string title, string message, NotificationType type = NotificationType.Information, NotificationPriority priority = NotificationPriority.Normal);

        /// <summary>
        /// Sends a notification to users with a specific role
        /// </summary>
        /// <param name="role">Role to notify</param>
        /// <param name="title">Notification title</param>
        /// <param name="message">Notification message</param>
        /// <param name="type">Type of notification</param>
        /// <param name="priority">Priority level</param>
        /// <returns>Task representing the async operation</returns>
        Task SendNotificationToRoleAsync(string role, string title, string message, NotificationType type = NotificationType.Information, NotificationPriority priority = NotificationPriority.Normal);

        /// <summary>
        /// Sends a notification to all users
        /// </summary>
        /// <param name="title">Notification title</param>
        /// <param name="message">Notification message</param>
        /// <param name="type">Type of notification</param>
        /// <param name="priority">Priority level</param>
        /// <returns>Task representing the async operation</returns>
        Task SendBroadcastNotificationAsync(string title, string message, NotificationType type = NotificationType.Information, NotificationPriority priority = NotificationPriority.Normal);

        /// <summary>
        /// Gets notifications for a specific user
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="includeRead">Whether to include read notifications</param>
        /// <param name="limit">Maximum number of notifications to return</param>
        /// <returns>List of notifications</returns>
        Task<List<Notification>> GetNotificationsAsync(int userId, bool includeRead = false, int limit = 50);

        /// <summary>
        /// Marks a notification as read
        /// </summary>
        /// <param name="notificationId">Notification ID</param>
        /// <param name="userId">User ID</param>
        /// <returns>Task representing the async operation</returns>
        Task MarkAsReadAsync(int notificationId, int userId);

        /// <summary>
        /// Marks all notifications as read for a user
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>Task representing the async operation</returns>
        Task MarkAllAsReadAsync(int userId);

        /// <summary>
        /// Dismisses a notification
        /// </summary>
        /// <param name="notificationId">Notification ID</param>
        /// <param name="userId">User ID</param>
        /// <returns>Task representing the async operation</returns>
        Task DismissNotificationAsync(int notificationId, int userId);

        /// <summary>
        /// Gets the count of unread notifications for a user
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>Number of unread notifications</returns>
        Task<int> GetUnreadCountAsync(int userId);

        /// <summary>
        /// Sends a desktop notification (toast)
        /// </summary>
        /// <param name="title">Notification title</param>
        /// <param name="message">Notification message</param>
        /// <param name="type">Type of notification</param>
        void ShowDesktopNotification(string title, string message, NotificationType type = NotificationType.Information);

        /// <summary>
        /// Sends an email notification
        /// </summary>
        /// <param name="email">Email address</param>
        /// <param name="subject">Email subject</param>
        /// <param name="body">Email body</param>
        /// <returns>Task representing the async operation</returns>
        Task SendEmailNotificationAsync(string email, string subject, string body);

        /// <summary>
        /// Sends an SMS notification
        /// </summary>
        /// <param name="phoneNumber">Phone number</param>
        /// <param name="message">SMS message</param>
        /// <returns>Task representing the async operation</returns>
        Task SendSMSNotificationAsync(string phoneNumber, string message);

        /// <summary>
        /// Creates a notification template
        /// </summary>
        /// <param name="template">Notification template</param>
        /// <returns>Task representing the async operation</returns>
        Task CreateTemplateAsync(NotificationTemplate template);

        /// <summary>
        /// Sends a notification using a template
        /// </summary>
        /// <param name="templateId">Template ID</param>
        /// <param name="userId">User ID</param>
        /// <param name="parameters">Template parameters</param>
        /// <returns>Task representing the async operation</returns>
        Task SendTemplatedNotificationAsync(int templateId, int userId, Dictionary<string, object> parameters);

        /// <summary>
        /// Schedules a notification to be sent later
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="title">Notification title</param>
        /// <param name="message">Notification message</param>
        /// <param name="scheduledTime">When to send the notification</param>
        /// <param name="type">Type of notification</param>
        /// <param name="priority">Priority level</param>
        /// <returns>Task representing the async operation</returns>
        Task ScheduleNotificationAsync(int userId, string title, string message, DateTime scheduledTime, NotificationType type = NotificationType.Information, NotificationPriority priority = NotificationPriority.Normal);

        /// <summary>
        /// Cancels a scheduled notification
        /// </summary>
        /// <param name="notificationId">Notification ID</param>
        /// <returns>Task representing the async operation</returns>
        Task CancelScheduledNotificationAsync(int notificationId);

        /// <summary>
        /// Gets notification preferences for a user
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>Notification preferences</returns>
        Task<NotificationPreferences> GetPreferencesAsync(int userId);

        /// <summary>
        /// Updates notification preferences for a user
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="preferences">New preferences</param>
        /// <returns>Task representing the async operation</returns>
        Task UpdatePreferencesAsync(int userId, NotificationPreferences preferences);

        /// <summary>
        /// Event fired when a new notification is created
        /// </summary>
        event EventHandler<NotificationEventArgs> NotificationCreated;

        /// <summary>
        /// Event fired when a notification is read
        /// </summary>
        event EventHandler<NotificationEventArgs> NotificationRead;

        /// <summary>
        /// Event fired when a notification is dismissed
        /// </summary>
        event EventHandler<NotificationEventArgs> NotificationDismissed;
    }

    /// <summary>
    /// Notification preferences for a user
    /// </summary>
    public class NotificationPreferences
    {
        /// <summary>
        /// User ID
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// Whether to receive desktop notifications
        /// </summary>
        public bool EnableDesktopNotifications { get; set; } = true;

        /// <summary>
        /// Whether to receive email notifications
        /// </summary>
        public bool EnableEmailNotifications { get; set; } = true;

        /// <summary>
        /// Whether to receive SMS notifications
        /// </summary>
        public bool EnableSMSNotifications { get; set; } = false;

        /// <summary>
        /// Whether to receive push notifications
        /// </summary>
        public bool EnablePushNotifications { get; set; } = true;

        /// <summary>
        /// Minimum priority level for notifications
        /// </summary>
        public NotificationPriority MinimumPriority { get; set; } = NotificationPriority.Low;

        /// <summary>
        /// Quiet hours start time
        /// </summary>
        public TimeSpan? QuietHoursStart { get; set; }

        /// <summary>
        /// Quiet hours end time
        /// </summary>
        public TimeSpan? QuietHoursEnd { get; set; }

        /// <summary>
        /// Whether to group similar notifications
        /// </summary>
        public bool GroupSimilarNotifications { get; set; } = true;

        /// <summary>
        /// Auto-dismiss notifications after this many minutes (0 = never)
        /// </summary>
        public int AutoDismissMinutes { get; set; } = 0;
    }
}
