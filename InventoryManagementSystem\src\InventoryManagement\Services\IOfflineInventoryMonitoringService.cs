using InventoryManagement.Models;
using InventoryManagement.Models.Reports;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace InventoryManagement.Services
{
    /// <summary>
    /// Interface for offline inventory monitoring service
    /// </summary>
    public interface IOfflineInventoryMonitoringService
    {
        /// <summary>
        /// Gets a list of low stock alerts
        /// </summary>
        /// <returns>List of low stock alerts</returns>
        Task<List<InventoryAlert>> GetLowStockAlertsAsync();

        /// <summary>
        /// Gets a list of expiring stock alerts
        /// </summary>
        /// <param name="daysThreshold">Days threshold for expiration</param>
        /// <returns>List of expiring stock alerts</returns>
        Task<List<InventoryAlert>> GetExpiringStockAlertsAsync(int daysThreshold = 30);

        /// <summary>
        /// Gets a list of overstock alerts
        /// </summary>
        /// <returns>List of overstock alerts</returns>
        Task<List<InventoryAlert>> GetOverstockAlertsAsync();

        /// <summary>
        /// Gets all active alerts
        /// </summary>
        /// <returns>List of all active alerts</returns>
        Task<List<InventoryAlert>> GetAllActiveAlertsAsync();

        /// <summary>
        /// Acknowledges an alert
        /// </summary>
        /// <param name="alertId">Alert ID</param>
        /// <param name="userId">User ID</param>
        /// <returns>True if successful</returns>
        Task<bool> AcknowledgeAlertAsync(int alertId, int userId);

        /// <summary>
        /// Processes all alerts and generates notifications
        /// </summary>
        /// <returns>True if successful</returns>
        Task<bool> ProcessAlertsAsync();

        /// <summary>
        /// Generates an inventory health report
        /// </summary>
        /// <returns>Inventory health report</returns>
        Task<InventoryHealthReport> GenerateInventoryHealthReportAsync();
    }

    /// <summary>
    /// Inventory health report
    /// </summary>
    public class InventoryHealthReport
    {
        /// <summary>
        /// Date the report was generated
        /// </summary>
        public DateTime GeneratedDate { get; set; }

        /// <summary>
        /// List of low stock items
        /// </summary>
        public List<LowStockItem> LowStockItems { get; set; } = new List<LowStockItem>();

        /// <summary>
        /// Count of low stock items
        /// </summary>
        public int LowStockItemsCount { get; set; }

        /// <summary>
        /// List of expiring items
        /// </summary>
        public List<ExpiringItem> ExpiringItems { get; set; } = new List<ExpiringItem>();

        /// <summary>
        /// Count of expiring items
        /// </summary>
        public int ExpiringItemsCount { get; set; }

        /// <summary>
        /// Total number of items
        /// </summary>
        public int TotalItems { get; set; }

        /// <summary>
        /// Total stock quantity
        /// </summary>
        public int TotalStock { get; set; }

        /// <summary>
        /// Total inventory value
        /// </summary>
        public decimal TotalValue { get; set; }

        /// <summary>
        /// Error message if any
        /// </summary>
        public string ErrorMessage { get; set; }
    }


}
