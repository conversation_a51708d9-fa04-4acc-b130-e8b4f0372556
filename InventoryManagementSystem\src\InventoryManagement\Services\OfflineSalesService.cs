using InventoryManagement.DataAccess;
using InventoryManagement.Models;
using InventoryManagement.Models.Reports;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace InventoryManagement.Services
{
    /// <summary>
    /// Offline implementation of sales service for pure offline operation
    /// </summary>
    public class OfflineSalesService : ISalesService
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly ILogger<OfflineSalesService> _logger;

        public OfflineSalesService(ApplicationDbContext dbContext, ILogger<OfflineSalesService> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        public async Task<List<SalesTransaction>> GetSalesForPeriodAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                _logger.LogInformation("Getting sales for period {StartDate} to {EndDate}", startDate, endDate);

                var sales = await _dbContext.Sales
                    .Include(s => s.User)
                    .Where(s => s.SaleDate >= startDate && s.SaleDate <= endDate)
                    .Select(s => new SalesTransaction
                    {
                        Id = s.Id,
                        UserId = s.UserId,
                        UserName = s.User != null ? s.User.FullName : "Unknown",
                        LocationId = s.LocationId ?? 1,
                        LocationName = "Main Location", // Default location
                        TransactionDate = s.SaleDate,
                        TotalAmount = s.TotalAmount,
                        PaymentMethod = s.PaymentMethod.ToString(),
                        TransactionStatus = "Completed"
                    })
                    .ToListAsync();

                _logger.LogInformation("Found {Count} sales transactions", sales.Count);
                return sales;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting sales for period");
                return new List<SalesTransaction>();
            }
        }

        public async Task<List<SalesDetail>> GetDetailedSalesForPeriodAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                _logger.LogInformation("Getting detailed sales for period {StartDate} to {EndDate}", startDate, endDate);

                var salesDetails = await _dbContext.Sales
                    .Include(s => s.SaleItems)
                    .ThenInclude(si => si.Product)
                    .Include(s => s.User)
                    .Where(s => s.SaleDate >= startDate && s.SaleDate <= endDate)
                    .SelectMany(s => s.SaleItems.Select(si => new SalesDetail
                    {
                        SaleId = s.Id,
                        SaleDate = s.SaleDate,
                        UserId = s.UserId,
                        UserName = s.User != null ? s.User.FullName : "Unknown",
                        ProductId = si.ProductId,
                        ProductName = si.Product != null ? si.Product.Name : "Unknown Product",
                        ProductCode = si.Product != null ? si.Product.Code : "",
                        Quantity = si.Quantity,
                        UnitPrice = si.UnitPrice,
                        TotalPrice = si.TotalPrice,
                        PaymentMethod = s.PaymentMethod.ToString()
                    }))
                    .ToListAsync();

                _logger.LogInformation("Found {Count} detailed sales records", salesDetails.Count);
                return salesDetails;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting detailed sales for period");
                return new List<SalesDetail>();
            }
        }

        public async Task<List<SalesTransaction>> GetSalesByCashierAsync(int cashierId, DateTime startDate, DateTime endDate)
        {
            try
            {
                _logger.LogInformation("Getting sales for cashier {CashierId} from {StartDate} to {EndDate}", 
                    cashierId, startDate, endDate);

                var sales = await _dbContext.Sales
                    .Include(s => s.User)
                    .Where(s => s.UserId == cashierId && s.SaleDate >= startDate && s.SaleDate <= endDate)
                    .Select(s => new SalesTransaction
                    {
                        Id = s.Id,
                        UserId = s.UserId,
                        UserName = s.User != null ? s.User.FullName : "Unknown",
                        LocationId = s.LocationId ?? 1,
                        LocationName = "Main Location",
                        TransactionDate = s.SaleDate,
                        TotalAmount = s.TotalAmount,
                        PaymentMethod = s.PaymentMethod.ToString(),
                        TransactionStatus = "Completed"
                    })
                    .ToListAsync();

                _logger.LogInformation("Found {Count} sales for cashier {CashierId}", sales.Count, cashierId);
                return sales;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting sales for cashier {CashierId}", cashierId);
                return new List<SalesTransaction>();
            }
        }

        public async Task<List<SalesTransaction>> GetSalesByLocationAsync(int locationId, DateTime startDate, DateTime endDate)
        {
            try
            {
                _logger.LogInformation("Getting sales for location {LocationId} from {StartDate} to {EndDate}", 
                    locationId, startDate, endDate);

                var sales = await _dbContext.Sales
                    .Include(s => s.User)
                    .Where(s => (s.LocationId ?? 1) == locationId && s.SaleDate >= startDate && s.SaleDate <= endDate)
                    .Select(s => new SalesTransaction
                    {
                        Id = s.Id,
                        UserId = s.UserId,
                        UserName = s.User != null ? s.User.FullName : "Unknown",
                        LocationId = s.LocationId ?? 1,
                        LocationName = "Main Location",
                        TransactionDate = s.SaleDate,
                        TotalAmount = s.TotalAmount,
                        PaymentMethod = s.PaymentMethod.ToString(),
                        TransactionStatus = "Completed"
                    })
                    .ToListAsync();

                _logger.LogInformation("Found {Count} sales for location {LocationId}", sales.Count, locationId);
                return sales;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting sales for location {LocationId}", locationId);
                return new List<SalesTransaction>();
            }
        }

        // Missing ISalesService interface methods
        public async Task<IEnumerable<Sale>> GetSalesByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                return await _dbContext.Sales
                    .Where(s => s.SaleDate >= startDate && s.SaleDate <= endDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting sales by date range");
                return new List<Sale>();
            }
        }

        public async Task<SalesSummary> GetSalesSummaryAsync(DateTime startDate, DateTime endDate, int? locationId = null)
        {
            try
            {
                var query = _dbContext.Sales.Where(s => s.SaleDate >= startDate && s.SaleDate <= endDate);
                if (locationId.HasValue)
                    query = query.Where(s => s.LocationId == locationId);

                var sales = await query.ToListAsync();

                return new SalesSummary
                {
                    TotalSales = sales.Sum(s => s.TotalAmount),
                    TotalTransactions = sales.Count,
                    AverageTransactionValue = sales.Any() ? sales.Average(s => s.TotalAmount) : 0,
                    StartDate = startDate,
                    EndDate = endDate
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting sales summary");
                return new SalesSummary();
            }
        }

        public async Task<(bool Success, string Message, Transaction Transaction)> CreateSaleAsync(
            List<SaleItem> items,
            PaymentMethod paymentMethod,
            decimal amountPaid,
            string customerName = null,
            string customerPhone = null,
            string notes = null,
            int userId = 0)
        {
            try
            {
                // Create a new sale
                var sale = new Sale
                {
                    UserId = userId,
                    SaleDate = DateTime.Now,
                    TotalAmount = items.Sum(i => i.Quantity * i.UnitPrice),
                    AmountPaid = amountPaid,
                    PaymentMethod = paymentMethod.ToString(),
                    CustomerName = customerName,
                    CustomerPhone = customerPhone,
                    Notes = notes,
                    Status = SaleStatus.Completed
                };

                _dbContext.Sales.Add(sale);
                await _dbContext.SaveChangesAsync();

                // Create corresponding transaction
                var transaction = new Transaction
                {
                    Type = TransactionType.Sale,
                    Amount = sale.TotalAmount,
                    TransactionDate = sale.SaleDate,
                    UserId = userId,
                    Notes = $"Sale #{sale.Id}"
                };

                return (true, "Sale created successfully", transaction);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating sale");
                return (false, ex.Message, null);
            }
        }

        public async Task<(bool Success, string Message)> VoidTransactionAsync(int transactionId, string reason, int userId)
        {
            try
            {
                var sale = await _dbContext.Sales.FindAsync(transactionId);
                if (sale == null)
                    return (false, "Sale not found");

                sale.Status = SaleStatus.Voided;
                sale.Notes = $"{sale.Notes} - VOIDED: {reason}";

                await _dbContext.SaveChangesAsync();
                return (true, "Transaction voided successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error voiding transaction");
                return (false, ex.Message);
            }
        }

        public async Task<SalesPerformanceMetrics> GetSalesPerformanceAsync(DateTime startDate, DateTime endDate, int? locationId = null)
        {
            try
            {
                var query = _dbContext.Sales.Where(s => s.SaleDate >= startDate && s.SaleDate <= endDate);
                if (locationId.HasValue)
                    query = query.Where(s => s.LocationId == locationId);

                var sales = await query.Include(s => s.SaleItems).ToListAsync();

                return new SalesPerformanceMetrics
                {
                    TotalSales = sales.Count,
                    TotalSalesAmount = sales.Sum(s => s.TotalAmount),
                    AverageSaleAmount = sales.Any() ? sales.Average(s => s.TotalAmount) : 0,
                    TotalItemsSold = sales.SelectMany(s => s.SaleItems).Sum(si => si.Quantity),
                    FromDate = startDate,
                    ToDate = endDate
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting sales performance");
                return new SalesPerformanceMetrics();
            }
        }

        public async Task<(int UniqueCustomers, int NewCustomers, int ReturningCustomers, List<TopCustomer> TopCustomers)> GetCustomerAnalyticsAsync(DateTime fromDate, DateTime toDate, int count = 10)
        {
            try
            {
                var sales = await _dbContext.Sales
                    .Where(s => s.SaleDate >= fromDate && s.SaleDate <= toDate && !string.IsNullOrEmpty(s.CustomerName))
                    .ToListAsync();

                var uniqueCustomers = sales.Select(s => s.CustomerName).Distinct().Count();
                var topCustomers = sales
                    .GroupBy(s => s.CustomerName)
                    .Select(g => new TopCustomer
                    {
                        CustomerName = g.Key,
                        TotalPurchases = g.Sum(s => s.TotalAmount),
                        TransactionCount = g.Count(),
                        AverageTransactionValue = g.Average(s => s.TotalAmount),
                        LastPurchaseDate = g.Max(s => s.SaleDate)
                    })
                    .OrderByDescending(tc => tc.TotalPurchases)
                    .Take(count)
                    .ToList();

                return (uniqueCustomers, 0, 0, topCustomers); // New/returning customer logic would need customer tracking
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting customer analytics");
                return (0, 0, 0, new List<TopCustomer>());
            }
        }

        public async Task<DailySales> GenerateDailySalesSummaryAsync(DateTime date, List<int>? locationIds = null)
        {
            try
            {
                var startDate = date.Date;
                var endDate = startDate.AddDays(1);

                var query = _dbContext.Sales.Where(s => s.SaleDate >= startDate && s.SaleDate < endDate);
                if (locationIds != null && locationIds.Any())
                    query = query.Where(s => locationIds.Contains(s.LocationId ?? 1));

                var sales = await query.Include(s => s.SaleItems).ToListAsync();

                return new DailySales
                {
                    Date = date,
                    DayOfWeek = date.DayOfWeek.ToString(),
                    Sales = sales.Sum(s => s.TotalAmount),
                    TransactionCount = sales.Count,
                    AverageTransactionValue = sales.Any() ? sales.Average(s => s.TotalAmount) : 0,
                    ItemsSold = sales.SelectMany(s => s.SaleItems).Sum(si => si.Quantity)
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating daily sales summary");
                return new DailySales { Date = date };
            }
        }

        public async Task<SalesReport> GenerateSalesReportAsync(SalesReportParameters parameters)
        {
            try
            {
                var summary = await GetSalesSummaryAsync(parameters.FromDate, parameters.ToDate);
                var performance = await GetSalesPerformanceAsync(parameters.FromDate, parameters.ToDate);
                var customerAnalytics = await GetCustomerAnalyticsAsync(parameters.FromDate, parameters.ToDate, parameters.TopCustomersCount);

                return new SalesReport
                {
                    ReportDate = DateTime.Now,
                    FromDate = parameters.FromDate,
                    ToDate = parameters.ToDate,
                    TotalSales = summary.TotalSales,
                    TotalTransactions = summary.TotalTransactions,
                    AverageTransactionValue = summary.AverageTransactionValue,
                    TopCustomers = customerAnalytics.TopCustomers
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating sales report");
                return new SalesReport();
            }
        }

        public async Task<byte[]> ExportSalesReportAsync(SalesReport report, SalesReportFormat format)
        {
            try
            {
                // Simple CSV export for now
                var csv = $"Report Date,{report.ReportDate}\n";
                csv += $"From Date,{report.FromDate}\n";
                csv += $"To Date,{report.ToDate}\n";
                csv += $"Total Sales,{report.TotalSales}\n";
                csv += $"Total Transactions,{report.TotalTransactions}\n";
                csv += $"Average Transaction Value,{report.AverageTransactionValue}\n";

                return System.Text.Encoding.UTF8.GetBytes(csv);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting sales report");
                return new byte[0];
            }
        }
    }
}
