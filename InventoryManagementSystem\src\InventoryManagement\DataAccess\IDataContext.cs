using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using InventoryManagement.Models;

namespace InventoryManagement.DataAccess
{
    /// <summary>
    /// Interface for data context operations in the inventory management system
    /// </summary>
    public interface IDataContext : IDisposable
    {
        // Core DbSets
        DbSet<User> Users { get; }
        DbSet<Item> Items { get; }
        DbSet<Category> Categories { get; }
        DbSet<ItemStock> ItemStocks { get; }
        DbSet<Location> Locations { get; }
        DbSet<Transaction> Transactions { get; }
        DbSet<TransactionDetail> TransactionDetails { get; }
        DbSet<Payment> Payments { get; }
        DbSet<BankAccount> BankAccounts { get; }
        DbSet<ExpenseCategory> ExpenseCategories { get; }
        DbSet<ItemExchange> ItemExchanges { get; }
        DbSet<ItemExchangeDetail> ItemExchangeDetails { get; }
        DbSet<DefectiveItem> DefectiveItems { get; }
        DbSet<DefectiveItemStatusHistory> DefectiveItemStatusHistory { get; }

        // Additional DbSets
        DbSet<AuditLog> AuditLogs { get; }
        DbSet<Models.ReportTemplate> ReportTemplates { get; }
        DbSet<Models.Notification> Notifications { get; }
        DbSet<NotificationTemplate> NotificationTemplates { get; }
        DbSet<Models.InventoryMetrics> InventoryMetrics { get; }
        DbSet<StockCount> StockCounts { get; }
        DbSet<Models.Inventory> Inventory { get; }
        DbSet<Models.AppSetting> AppSettings { get; }
        DbSet<Models.Vendor> Vendors { get; }
        DbSet<Models.Permission> Permissions { get; }
        DbSet<Models.ItemSupplier> ItemSuppliers { get; }
        DbSet<Models.UserSession> UserSessions { get; }

        // Sales entities
        DbSet<SalesTransaction> SalesTransactions { get; }
        DbSet<SalesDetail> SalesDetails { get; }
        DbSet<ExpiringItem> ExpiringItems { get; }

        // Change tracking
        ChangeTracker ChangeTracker { get; }

        // Database operations
        Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
        int SaveChanges();

        // Entity operations
        EntityEntry<TEntity> Entry<TEntity>(TEntity entity) where TEntity : class;
        EntityEntry Entry(object entity);

        // Database access
        Microsoft.EntityFrameworkCore.Infrastructure.DatabaseFacade Database { get; }

        // Set operations
        DbSet<TEntity> Set<TEntity>() where TEntity : class;
    }
}
